#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web邮件功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).resolve().parent))

from web.email_auth import authenticate_email_user


def test_email_authentication():
    """测试邮件认证功能"""
    print("🔍 测试邮件认证功能")
    print("-" * 40)
    
    # 测试邮箱（请替换为真实的邮箱和授权码）
    test_email = input("请输入测试邮箱地址: ").strip()
    if not test_email:
        print("❌ 邮箱地址不能为空")
        return False
    
    import getpass
    test_password = getpass.getpass("请输入邮箱授权码: ")
    if not test_password:
        print("❌ 授权码不能为空")
        return False
    
    print(f"🔐 正在认证邮箱: {test_email}")
    
    # 进行认证
    user = authenticate_email_user(test_email, test_password)
    
    if user:
        print(f"✅ 邮箱认证成功!")
        print(f"   邮箱: {user.email}")
        print(f"   服务商: {user.provider_name}")
        
        # 测试配置获取
        smtp_config = user.get_smtp_config()
        pop3_config = user.get_pop3_config()
        
        print(f"   SMTP: {smtp_config.get('host')}:{smtp_config.get('port')}")
        print(f"   POP3: {pop3_config.get('host')}:{pop3_config.get('port')}")
        
        return True
    else:
        print("❌ 邮箱认证失败")
        return False


def test_pop3_connection():
    """测试POP3连接"""
    print("\n🔍 测试POP3连接功能")
    print("-" * 40)
    
    # 获取认证信息
    test_email = input("请输入测试邮箱地址: ").strip()
    if not test_email:
        print("❌ 邮箱地址不能为空")
        return False
    
    import getpass
    test_password = getpass.getpass("请输入邮箱授权码: ")
    if not test_password:
        print("❌ 授权码不能为空")
        return False
    
    # 认证用户
    user = authenticate_email_user(test_email, test_password)
    if not user:
        print("❌ 邮箱认证失败")
        return False
    
    # 测试POP3连接
    try:
        import poplib
        
        pop3_config = user.get_pop3_config()
        print(f"🔄 正在连接到 {pop3_config['host']}:{pop3_config['port']}")
        
        if pop3_config.get("use_ssl"):
            server = poplib.POP3_SSL(pop3_config["host"], pop3_config["port"])
        else:
            server = poplib.POP3(pop3_config["host"], pop3_config["port"])
        
        # 登录
        server.user(user.email)
        server.pass_(pop3_config["password"])
        
        # 获取邮箱状态
        num_messages = len(server.list()[1])
        print(f"✅ POP3连接成功!")
        print(f"   邮箱中有 {num_messages} 封邮件")
        
        server.quit()
        return True
        
    except Exception as e:
        print(f"❌ POP3连接失败: {e}")
        return False


def test_smtp_connection():
    """测试SMTP连接"""
    print("\n🔍 测试SMTP连接功能")
    print("-" * 40)
    
    # 获取认证信息
    test_email = input("请输入测试邮箱地址: ").strip()
    if not test_email:
        print("❌ 邮箱地址不能为空")
        return False
    
    import getpass
    test_password = getpass.getpass("请输入邮箱授权码: ")
    if not test_password:
        print("❌ 授权码不能为空")
        return False
    
    # 认证用户
    user = authenticate_email_user(test_email, test_password)
    if not user:
        print("❌ 邮箱认证失败")
        return False
    
    # 测试SMTP连接
    try:
        import smtplib
        
        smtp_config = user.get_smtp_config()
        print(f"🔄 正在连接到 {smtp_config['host']}:{smtp_config['port']}")
        
        server = smtplib.SMTP(smtp_config["host"], smtp_config["port"])
        if smtp_config.get("use_tls"):
            server.starttls()
        
        server.login(user.email, smtp_config["password"])
        print(f"✅ SMTP连接成功!")
        
        server.quit()
        return True
        
    except Exception as e:
        print(f"❌ SMTP连接失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Web邮件功能测试")
    print("=" * 50)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试邮件认证")
        print("2. 测试POP3连接")
        print("3. 测试SMTP连接")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            test_email_authentication()
        elif choice == "2":
            test_pop3_connection()
        elif choice == "3":
            test_smtp_connection()
        elif choice == "4":
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main()
