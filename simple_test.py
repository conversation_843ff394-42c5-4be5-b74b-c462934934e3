#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的邮件认证测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).resolve().parent))

try:
    from email_providers_config import get_provider_config
    print("✅ 邮件服务商配置导入成功")
    
    # 测试配置获取
    test_config = get_provider_config("<EMAIL>")
    if test_config:
        print(f"✅ QQ邮箱配置获取成功: {test_config['name']}")
    else:
        print("❌ QQ邮箱配置获取失败")
        
except Exception as e:
    print(f"❌ 邮件服务商配置导入失败: {e}")
    import traceback
    traceback.print_exc()

try:
    from web.email_auth import EmailAuthenticator
    print("✅ 邮件认证器导入成功")
    
    # 创建认证器
    authenticator = EmailAuthenticator()
    print("✅ 邮件认证器创建成功")
    
except Exception as e:
    print(f"❌ 邮件认证器导入失败: {e}")
    import traceback
    traceback.print_exc()

print("\n🔍 基础功能测试完成")
