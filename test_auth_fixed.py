#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的邮件认证测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).resolve().parent))

# 强制重新加载模块
if 'web.email_auth' in sys.modules:
    del sys.modules['web.email_auth']

try:
    from web.email_auth import authenticate_email_user
    print("✅ 邮件认证模块导入成功")
    
    # 测试认证
    print("🔍 测试邮件认证功能")
    test_email = "<EMAIL>"
    test_password = "your_auth_code_here"  # 请替换为真实的授权码
    
    print(f"🔐 正在认证邮箱: {test_email}")
    user = authenticate_email_user(test_email, test_password)
    
    if user:
        print(f"✅ 邮箱认证成功!")
        print(f"   邮箱: {user.email}")
        print(f"   服务商: {user.provider_name}")
        
        # 测试配置获取
        smtp_config = user.get_smtp_config()
        pop3_config = user.get_pop3_config()
        
        print(f"   SMTP: {smtp_config.get('host')}:{smtp_config.get('port')}")
        print(f"   POP3: {pop3_config.get('host')}:{pop3_config.get('port')}")
        print(f"   needs_reauth: {user.needs_reauth}")
        
    else:
        print("❌ 邮箱认证失败")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n🔍 测试完成")
